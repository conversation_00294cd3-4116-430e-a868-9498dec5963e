<!DOCTYPE html>
<html>
<head>
    <title>Test Coordinate Axes</title>
    <style>
        body { margin: 0; background: black; }
        canvas { display: block; }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-family: Arial;
        }
    </style>
</head>
<body>
    <canvas id="canvas" width="800" height="600"></canvas>
    <div id="info">
        Press X to toggle axes<br>
        Use mouse to rotate view
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const gl = canvas.getContext('webgl2');

        if (!gl) {
            alert('WebGL2 not supported');
        }

        // 坐标轴着色器
        const axisVertexShaderSource = `
        #version 300 es
        precision highp float;

        uniform mat4 projection;
        uniform mat4 view;

        in vec3 position;
        in vec3 color;

        out vec3 vColor;

        void main() {
            gl_Position = projection * view * vec4(position, 1.0);
            vColor = color;
        }
        `;

        const axisFragmentShaderSource = `
        #version 300 es
        precision highp float;

        in vec3 vColor;
        out vec4 fragColor;

        void main() {
            fragColor = vec4(vColor, 1.0);
        }
        `;

        // 创建着色器
        function createShader(gl, type, source) {
            const shader = gl.createShader(type);
            gl.shaderSource(shader, source);
            gl.compileShader(shader);
            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                console.error(gl.getShaderInfoLog(shader));
                gl.deleteShader(shader);
                return null;
            }
            return shader;
        }

        // 创建程序
        function createProgram(gl, vertexShader, fragmentShader) {
            const program = gl.createProgram();
            gl.attachShader(program, vertexShader);
            gl.attachShader(program, fragmentShader);
            gl.linkProgram(program);
            if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                console.error(gl.getProgramInfoLog(program));
                gl.deleteProgram(program);
                return null;
            }
            return program;
        }

        // 创建坐标轴着色器程序
        const axisVertexShader = createShader(gl, gl.VERTEX_SHADER, axisVertexShaderSource);
        const axisFragmentShader = createShader(gl, gl.FRAGMENT_SHADER, axisFragmentShaderSource);
        const axisProgram = createProgram(gl, axisVertexShader, axisFragmentShader);

        // 坐标轴几何数据
        const axisLength = 2.0;
        const axisVertices = new Float32Array([
            // X轴 (红色)
            0.0, 0.0, 0.0,  1.0, 0.0, 0.0,
            axisLength, 0.0, 0.0,  1.0, 0.0, 0.0,
            
            // Y轴 (绿色)
            0.0, 0.0, 0.0,  0.0, 1.0, 0.0,
            0.0, axisLength, 0.0,  0.0, 1.0, 0.0,
            
            // Z轴 (蓝色)
            0.0, 0.0, 0.0,  0.0, 0.0, 1.0,
            0.0, 0.0, axisLength,  0.0, 0.0, 1.0,
        ]);

        const axisVertexBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, axisVertexBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, axisVertices, gl.STATIC_DRAW);

        // 获取属性位置
        const a_position = gl.getAttribLocation(axisProgram, 'position');
        const a_color = gl.getAttribLocation(axisProgram, 'color');
        const u_projection = gl.getUniformLocation(axisProgram, 'projection');
        const u_view = gl.getUniformLocation(axisProgram, 'view');

        // 矩阵工具函数
        function perspective(fovy, aspect, near, far) {
            const f = 1.0 / Math.tan(fovy / 2);
            return [
                f / aspect, 0, 0, 0,
                0, f, 0, 0,
                0, 0, (far + near) / (near - far), -1,
                0, 0, (2 * far * near) / (near - far), 0
            ];
        }

        function lookAt(eye, center, up) {
            const zAxis = normalize(subtract(eye, center));
            const xAxis = normalize(cross(up, zAxis));
            const yAxis = cross(zAxis, xAxis);
            
            return [
                xAxis[0], yAxis[0], zAxis[0], 0,
                xAxis[1], yAxis[1], zAxis[1], 0,
                xAxis[2], yAxis[2], zAxis[2], 0,
                -dot(xAxis, eye), -dot(yAxis, eye), -dot(zAxis, eye), 1
            ];
        }

        function subtract(a, b) { return [a[0] - b[0], a[1] - b[1], a[2] - b[2]]; }
        function normalize(v) {
            const len = Math.sqrt(v[0] * v[0] + v[1] * v[1] + v[2] * v[2]);
            return [v[0] / len, v[1] / len, v[2] / len];
        }
        function cross(a, b) {
            return [a[1] * b[2] - a[2] * b[1], a[2] * b[0] - a[0] * b[2], a[0] * b[1] - a[1] * b[0]];
        }
        function dot(a, b) { return a[0] * b[0] + a[1] * b[1] + a[2] * b[2]; }

        // 相机参数
        let cameraDistance = 5;
        let cameraAngleX = 0;
        let cameraAngleY = 0;
        let showAxes = true;

        // 鼠标控制
        let mouseDown = false;
        let lastMouseX = 0;
        let lastMouseY = 0;

        canvas.addEventListener('mousedown', (e) => {
            mouseDown = true;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        });

        canvas.addEventListener('mouseup', () => {
            mouseDown = false;
        });

        canvas.addEventListener('mousemove', (e) => {
            if (mouseDown) {
                const deltaX = e.clientX - lastMouseX;
                const deltaY = e.clientY - lastMouseY;
                cameraAngleY += deltaX * 0.01;
                cameraAngleX += deltaY * 0.01;
                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
            }
        });

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (e.code === 'KeyX') {
                showAxes = !showAxes;
                console.log('Axes:', showAxes ? 'ON' : 'OFF');
            }
        });

        // 渲染循环
        function render() {
            gl.viewport(0, 0, canvas.width, canvas.height);
            gl.clearColor(0, 0, 0, 1);
            gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
            gl.enable(gl.DEPTH_TEST);

            if (showAxes) {
                // 计算相机位置
                const eye = [
                    Math.sin(cameraAngleY) * Math.cos(cameraAngleX) * cameraDistance,
                    Math.sin(cameraAngleX) * cameraDistance,
                    Math.cos(cameraAngleY) * Math.cos(cameraAngleX) * cameraDistance
                ];

                const projectionMatrix = perspective(Math.PI / 4, canvas.width / canvas.height, 0.1, 100);
                const viewMatrix = lookAt(eye, [0, 0, 0], [0, 1, 0]);

                gl.useProgram(axisProgram);
                gl.uniformMatrix4fv(u_projection, false, projectionMatrix);
                gl.uniformMatrix4fv(u_view, false, viewMatrix);

                gl.bindBuffer(gl.ARRAY_BUFFER, axisVertexBuffer);
                gl.enableVertexAttribArray(a_position);
                gl.enableVertexAttribArray(a_color);

                gl.vertexAttribPointer(a_position, 3, gl.FLOAT, false, 6 * 4, 0);
                gl.vertexAttribPointer(a_color, 3, gl.FLOAT, false, 6 * 4, 3 * 4);

                gl.drawArrays(gl.LINES, 0, 6);
            }

            requestAnimationFrame(render);
        }

        render();
    </script>
</body>
</html>
