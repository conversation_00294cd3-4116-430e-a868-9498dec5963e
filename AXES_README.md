# 坐标轴功能说明

## 功能概述

在3D Gaussian Splat查看器中添加了坐标轴显示功能，可以帮助用户更好地理解3D场景的空间方向。

## 功能特性

- **三轴显示**: 显示X、Y、Z三个坐标轴
- **颜色编码**: 
  - X轴: 红色
  - Y轴: 绿色  
  - Z轴: 蓝色
- **实时更新**: 坐标轴随相机视角实时更新
- **可切换显示**: 可以通过按键控制显示/隐藏

## 使用方法

### 键盘控制
- 按 `X` 键: 切换坐标轴的显示/隐藏状态
- 按 `C` 键: 切换相机位姿信息显示

### 状态指示
- 右上角显示当前坐标轴状态 ("Axes: ON" 或 "Axes: OFF")

## 技术实现

### 着色器
- 使用独立的顶点着色器和片段着色器渲染坐标轴
- 支持3D变换和透视投影

### 几何数据
- 每个轴由两个顶点组成（起点和终点）
- 轴长度为2.0单位
- 所有轴都从原点(0,0,0)开始

### 渲染特性
- 启用深度测试确保正确的遮挡关系
- 使用较粗的线宽(3.0)提高可见性
- 在主场景渲染后绘制，确保不影响主要内容

## 坐标系说明

- **X轴 (红色)**: 指向右方
- **Y轴 (绿色)**: 指向上方  
- **Z轴 (蓝色)**: 指向前方

这遵循右手坐标系的标准约定。

## 注意事项

1. 坐标轴始终从世界坐标原点(0,0,0)开始显示
2. 轴的长度是固定的，不会根据场景大小自动调整
3. 在某些视角下，坐标轴可能会被场景中的对象遮挡
4. 坐标轴的显示状态会在页面刷新后重置为默认状态(开启)

## 故障排除

如果坐标轴不显示：
1. 检查浏览器控制台是否有WebGL错误
2. 确认按了`X`键后状态指示器显示"Axes: ON"
3. 尝试调整相机视角，坐标轴可能在当前视角外
4. 检查浏览器是否支持WebGL2

## 扩展可能

未来可以考虑添加的功能：
- 坐标轴标签显示(X, Y, Z)
- 可调节的轴长度
- 网格显示
- 坐标轴位置调整
- 更多的视觉样式选项
